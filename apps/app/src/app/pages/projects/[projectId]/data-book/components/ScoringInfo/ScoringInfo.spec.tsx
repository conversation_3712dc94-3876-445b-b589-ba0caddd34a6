import React from 'react';
import { render, screen } from 'tests/test-utils';
import { DataHealthProvider } from '../../data-health-heatmap/DataHealthContext';
import { ScoringInfo } from './ScoringInfo';

describe('<ScoringInfo />', () => {
  describe('when record type is issues', () => {
    it('renders the issues scoring info', async () => {
      render(
        <DataHealthProvider recordType={'issues'} seriesLength={3}>
          <ScoringInfo />
        </DataHealthProvider>
      );

      expect(await screen.findByLabelText('issuesScoring.label')).toBeInTheDocument();
    });
  });
});
