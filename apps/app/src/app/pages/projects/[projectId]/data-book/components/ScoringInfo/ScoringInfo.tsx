import { useDataHealth } from '../../data-health-heatmap/DataHealthContext';
import { IssuesScoringInfoPopover } from './IssuesScoringInfoPopover';

export const useShowScoringIndicator = () => {
  const { recordType } = useDataHealth();

  switch (recordType) {
    case 'issues':
      return <IssuesScoringInfoPopover />;
    default:
      return null;
  }
};

export const ScoringInfo: React.FC = () => {
    return useShowScoringIndicator();
};
