import React from 'react';
import { dataHealthScoresFactory } from '@shape-construction/api/factories/dataHealthScores';
import { render, screen } from 'tests/test-utils';
import { DataHealthProvider } from '../../data-health-heatmap/DataHealthContext';
import { transformedHeatmapDataFactory } from '../../factories/transformedHeatmapData';
import { PerformanceMatrix } from './PerformanceMatrix';

describe('<PerformanceMatrix />', () => {
  it('renders the heatmap with data health scores', () => {
    const onCellClick = jest.fn();
    const inputData = dataHealthScoresFactory();
    const heatmapData = transformedHeatmapDataFactory();
    render(
      <DataHealthProvider recordType={'issues'} seriesLength={3}>
        <PerformanceMatrix data={heatmapData} onCellClick={onCellClick} />
      </DataHealthProvider>
    );

    expect(screen.getAllByText(/%/)).toHaveLength(11);
    expect(screen.getAllByText(/^-$/)).toHaveLength(5);
    expect(screen.getAllByRole('status')).toHaveLength(5);
    expect(
      screen.getAllByRole('button', { name: /^dataBook.page.heatmapDashboard.heatmap.cell.label-[0-9]{1,3}$/ })
    ).toHaveLength(inputData.entries.length);
  });

  it('renders team members column', () => {
    const onCellClick = jest.fn();
    const heatmapData = transformedHeatmapDataFactory();
    render(
      <DataHealthProvider recordType={'issues'} seriesLength={3}>
        <PerformanceMatrix data={heatmapData} onCellClick={onCellClick} />
      </DataHealthProvider>
    );

    heatmapData.forEach((row) => {
      const nameElements = screen.getAllByText(row.teamMember.user.name);
      expect(nameElements.length).toBeGreaterThan(0);
      if (row.constructionRole) {
        expect(screen.getByText(row.constructionRole.label)).toBeInTheDocument();
      }
    });
  });

  describe('when user clicks on a cell', () => {
    it('calls onClick with the data point', async () => {
      const onCellClick = jest.fn();
      const heatmapData = transformedHeatmapDataFactory();
      const { user } = render(
        <DataHealthProvider recordType={'issues'} seriesLength={3}>
          <PerformanceMatrix data={heatmapData} onCellClick={onCellClick} />
        </DataHealthProvider>
      );

      await user.click(
        screen.getByRole('button', {
          name: (_, el) => Boolean(el.textContent?.includes('67%')),
        })
      );

      expect(onCellClick).toHaveBeenCalledWith(
        expect.objectContaining({
          score: 67,
        })
      );
    });
  });

  describe('when entries are empty', () => {
    it('renders the empty state', async () => {
      const onCellClick = jest.fn();
      render(
        <DataHealthProvider recordType={'issues'} seriesLength={3}>
          <PerformanceMatrix data={[]} onCellClick={onCellClick} />
        </DataHealthProvider>
      );

      expect(await screen.findByText('dataBook.page.heatmapDashboard.heatmap.emptyState.title')).toBeInTheDocument();
      expect(await screen.findByText('dataBook.page.heatmapDashboard.heatmap.emptyState.body')).toBeInTheDocument();
    });
  });
});
