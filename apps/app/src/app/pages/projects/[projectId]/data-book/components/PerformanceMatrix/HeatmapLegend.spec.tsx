import React from 'react';
import { render, screen } from 'tests/test-utils';
import { DataHealthProvider } from '../../data-health-heatmap/DataHealthContext';
import { HeatmapLegend } from './HeatmapLegend';

describe('<HeatmapLegend />', () => {
  it('renders the heatmap legend', () => {
    render(
      <DataHealthProvider recordType={'issues'} seriesLength={3}>
        <HeatmapLegend />
      </DataHealthProvider>
    );

    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.1.label/ })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.2.label/ })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.3.label/ })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.4.label/ })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.5.label/ })
    ).toBeInTheDocument();
  });
});
